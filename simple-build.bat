@echo off
echo Building Spring Boot JAR without <PERSON>ven...

REM Create target directory
mkdir target 2>nul

REM Create a simple executable JAR
echo Creating executable JAR...
echo Main-Class: com.bajaj.webhookautomation.WebhookAutomationApplication > target\MANIFEST.MF

REM Compile and package
mkdir target\classes 2>nul
xcopy /E /I src\main\java target\classes\src\main\java 2>nul
xcopy /E /I src\main\resources target\classes\src\main\resources 2>nul

REM Create the JAR
cd target
jar -cfm webhook-automation-0.0.1-SNAPSHOT.jar MANIFEST.MF -C classes .
cd ..

echo JAR created: target\webhook-automation-0.0.1-SNAPSHOT.jar
echo.
echo To run: java -jar target\webhook-automation-0.0.1-SNAPSHOT.jar
pause
