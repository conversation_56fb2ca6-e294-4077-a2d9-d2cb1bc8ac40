package com.bajaj.webhookautomation.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SqlSubmissionRequest {
    
    @JsonProperty("query")
    private String query;
    
    public SqlSubmissionRequest() {}
    
    public SqlSubmissionRequest(String query) {
        this.query = query;
    }
    
    public String getQuery() {
        return query;
    }
    
    public void setQuery(String query) {
        this.query = query;
    }
}
