package com.bajaj.webhookautomation.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UserRegistrationRequest {
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("registrationNumber")
    private String registrationNumber;
    
    @JsonProperty("email")
    private String email;
    
    public UserRegistrationRequest() {}
    
    public UserRegistrationRequest(String name, String registrationNumber, String email) {
        this.name = name;
        this.registrationNumber = registrationNumber;
        this.email = email;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getRegistrationNumber() {
        return registrationNumber;
    }
    
    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
}
