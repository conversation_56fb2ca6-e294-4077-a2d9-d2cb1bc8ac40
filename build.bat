@echo off
echo Creating directories...
mkdir target\classes 2>nul
mkdir target\classes\com\bajaj\webhookautomation 2>nul
mkdir target\classes\com\bajaj\webhookautomation\dto 2>nul
mkdir target\classes\com\bajaj\webhookautomation\service 2>nul

echo Downloading Spring Boot dependencies...
curl -o target\spring-boot-starter-web-3.2.0.jar "https://repo1.maven.org/maven2/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar"
curl -o target\spring-boot-3.2.0.jar "https://repo1.maven.org/maven2/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar"
curl -o target\spring-boot-autoconfigure-3.2.0.jar "https://repo1.maven.org/maven2/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar"
curl -o target\spring-web-6.1.1.jar "https://repo1.maven.org/maven2/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar"
curl -o target\spring-core-6.1.1.jar "https://repo1.maven.org/maven2/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar"
curl -o target\jackson-databind-2.15.3.jar "https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar"

echo Compiling Java files...
javac -cp "target\*.jar" -d target\classes src\main\java\com\bajaj\webhookautomation\*.java src\main\java\com\bajaj\webhookautomation\dto\*.java src\main\java\com\bajaj\webhookautomation\service\*.java

echo Copying resources...
copy src\main\resources\application.properties target\classes\ 2>nul

echo Creating JAR file...
cd target\classes
jar -cfm ..\webhook-automation-0.0.1-SNAPSHOT.jar ..\..\MANIFEST.MF .
cd ..\..

echo JAR file created: target\webhook-automation-0.0.1-SNAPSHOT.jar
echo Done!
