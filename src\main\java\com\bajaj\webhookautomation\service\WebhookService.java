package com.bajaj.webhookautomation.service;

import com.bajaj.webhookautomation.dto.SqlSubmissionRequest;
import com.bajaj.webhookautomation.dto.UserRegistrationRequest;
import com.bajaj.webhookautomation.dto.WebhookResponse;
import org.springframework.boot.CommandLineRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
public class WebhookService implements CommandLineRunner {

    private static final String WEBHOOK_GENERATION_URL = "https://bfhldevapigw.healthrx.co.in/hiring/generateWebhook/JAVA";
    
    private static final String SQL_QUERY = """
        SELECT
            p.AMOUNT AS SALARY,
            CONCAT(e.FIRST_NAME, ' ', e.LAST_NAME) AS NAME,
            TIMESTAMPDIFF(YEAR, e.DOB, CURDATE()) AS AGE,
            d.DEPARTMENT_NAME
        FROM
            PAYMENTS p
        JOIN
            EMPLOYEE e ON p.EMP_ID = e.EMP_ID
        JOIN
            DEPARTMENT d ON e.DEPARTMENT = d.DEPARTMENT_ID
        WHERE
            EXTRACT(DAY FROM p.PAYMENT_TIME) != 1
        ORDER BY
            p.AMOUNT DESC
        LIMIT 1;
        """;

    private final RestTemplate restTemplate;

    public WebhookService() {
        this.restTemplate = new RestTemplate();
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("Starting webhook automation process...");
        
        try {
            // Step 1: Generate webhook
            WebhookResponse webhookResponse = generateWebhook();
            System.out.println("Webhook generated successfully!");
            System.out.println("Webhook URL: " + webhookResponse.getWebhookUrl());
            
            // Step 2: Submit SQL query
            submitSqlQuery(webhookResponse.getWebhookUrl(), webhookResponse.getAccessToken());
            System.out.println("SQL query submitted successfully!");
            
        } catch (Exception e) {
            System.err.println("Error during webhook automation: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private WebhookResponse generateWebhook() {
        // Create user registration request
        UserRegistrationRequest request = new UserRegistrationRequest(
            "John Doe",
            "REG12347", 
            "<EMAIL>"
        );

        // Set headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Create HTTP entity
        HttpEntity<UserRegistrationRequest> entity = new HttpEntity<>(request, headers);

        // Make POST request
        ResponseEntity<WebhookResponse> response = restTemplate.exchange(
            WEBHOOK_GENERATION_URL,
            HttpMethod.POST,
            entity,
            WebhookResponse.class
        );

        return response.getBody();
    }

    private void submitSqlQuery(String webhookUrl, String accessToken) {
        // Create SQL submission request
        SqlSubmissionRequest request = new SqlSubmissionRequest(SQL_QUERY);

        // Set headers with authorization
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", accessToken);

        // Create HTTP entity
        HttpEntity<SqlSubmissionRequest> entity = new HttpEntity<>(request, headers);

        // Make POST request to the dynamic webhook URL
        ResponseEntity<String> response = restTemplate.exchange(
            webhookUrl,
            HttpMethod.POST,
            entity,
            String.class
        );

        System.out.println("Response from SQL submission: " + response.getBody());
    }
}
